package corp.jamaro.jamaroservidor.app.caja.repository.gui;

import corp.jamaro.jamaroservidor.app.caja.model.gui.CajaGui;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface CajaGuiRepository extends ReactiveNeo4jRepository<CajaGui, UUID> {

    /**
     * Obtiene todas las CajaGui ordenadas por fecha de creación (más recientes primero).
     *
     * @return Flux que emite todas las CajaGui ordenadas por createdAt descendente
     */
    @Query("""
           MATCH (c:CajaGui)
           OPTIONAL MATCH (c)-[r1:CON_CAJA_DINERO_EFECTIVO]->(ce:CajaDineroEfectivo)
           OPTIONAL MATCH (c)-[r2:CON_CAJA_DINERO_DIGITAL]->(cd:CajaDineroDigital)
           RETURN c, r1, ce, r2, cd
           ORDER BY c.createdAt DESC
           """)
    Flux<CajaGui> findAllOrderedByCreatedAtDesc();

    /**
     * Busca una CajaGui por su UUID con todas sus relaciones cargadas.
     *
     * @param cajaGuiId El UUID de la CajaGui a buscar
     * @return Mono que emite la CajaGui con sus relaciones o vacío si no se encuentra
     */
    @Query("""
           MATCH (c:CajaGui) WHERE c.id = $cajaGuiId
           OPTIONAL MATCH (c)-[r1:CON_CAJA_DINERO_EFECTIVO]->(ce:CajaDineroEfectivo)
           OPTIONAL MATCH (c)-[r2:CON_CAJA_DINERO_DIGITAL]->(cd:CajaDineroDigital)
           RETURN c, r1, ce, r2, cd
           """)
    Mono<CajaGui> findByIdWithRelations(@Param("cajaGuiId") UUID cajaGuiId);





}
