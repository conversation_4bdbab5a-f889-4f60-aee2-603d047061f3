package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.model.gui.CajaGui;
import corp.jamaro.jamaroservidor.app.caja.service.CajaGuiService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CajaGuiControllerTest {

    @Mock
    private CajaGuiService cajaGuiService;

    private CajaGuiController cajaGuiController;

    @BeforeEach
    void setUp() {
        cajaGuiController = new CajaGuiController(cajaGuiService);
    }

    @Test
    void testSubscribeToCurrentCajaGui_Success() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);

        CajaGuiController.SubscribeToCajaGuiRequest request = 
                new CajaGuiController.SubscribeToCajaGuiRequest(cajaGuiId);

        when(cajaGuiService.subscribeToCurrentCajaGui(cajaGuiId))
                .thenReturn(Flux.just(cajaGui));

        // When & Then
        StepVerifier.create(cajaGuiController.subscribeToCurrentCajaGui(request))
                .expectNext(cajaGui)
                .verifyComplete();

        verify(cajaGuiService).subscribeToCurrentCajaGui(cajaGuiId);
    }

    @Test
    void testGetAllCajaGuiOrderedByCreatedAt_Success() {
        // Given
        CajaGui cajaGui1 = createTestCajaGui("Caja 1", Instant.now().minusSeconds(3600));
        CajaGui cajaGui2 = createTestCajaGui("Caja 2", Instant.now());

        when(cajaGuiService.getAllCajaGuiOrderedByCreatedAt())
                .thenReturn(Flux.just(cajaGui2, cajaGui1));

        // When & Then
        StepVerifier.create(cajaGuiController.getAllCajaGuiOrderedByCreatedAt())
                .expectNext(cajaGui2)
                .expectNext(cajaGui1)
                .verifyComplete();

        verify(cajaGuiService).getAllCajaGuiOrderedByCreatedAt();
    }

    @Test
    void testCreateCajaGui_Success() {
        // Given
        String nombreCaja = "Nueva Caja";
        String guiConfig = "{\"theme\":\"dark\"}";
        CajaGui savedCajaGui = createTestCajaGui(nombreCaja, Instant.now());
        savedCajaGui.setGuiConfig(guiConfig);

        CajaGuiController.CreateCajaGuiRequest request = 
                new CajaGuiController.CreateCajaGuiRequest(nombreCaja, guiConfig);

        when(cajaGuiService.createCajaGui(nombreCaja, guiConfig))
                .thenReturn(Mono.just(savedCajaGui));

        // When & Then
        StepVerifier.create(cajaGuiController.createCajaGui(request))
                .expectNext(savedCajaGui)
                .verifyComplete();

        verify(cajaGuiService).createCajaGui(nombreCaja, guiConfig);
    }

    @Test
    void testInitializeCajaDineroEfectivo_Success() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);

        CajaGuiController.InitializeCajaDineroEfectivoRequest request = 
                new CajaGuiController.InitializeCajaDineroEfectivoRequest(
                        cajaGuiId, "Caja Efectivo", 100.0,
                        0, 0, 0, 10, 5, 2, 1, 0, 0, 1, 0
                );

        when(cajaGuiService.initializeCajaDineroEfectivo(
                eq(cajaGuiId), eq("Caja Efectivo"), eq(100.0),
                eq(0), eq(0), eq(0), eq(10), eq(5), eq(2), eq(1), eq(0), eq(0), eq(1), eq(0)))
                .thenReturn(Mono.just(cajaGui));

        // When & Then
        StepVerifier.create(cajaGuiController.initializeCajaDineroEfectivo(request))
                .expectNext(cajaGui)
                .verifyComplete();

        verify(cajaGuiService).initializeCajaDineroEfectivo(
                cajaGuiId, "Caja Efectivo", 100.0,
                0, 0, 0, 10, 5, 2, 1, 0, 0, 1, 0);
    }

    @Test
    void testInitializeCajaDineroDigital_Success() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);

        CajaGuiController.InitializeCajaDineroDigitalRequest request = 
                new CajaGuiController.InitializeCajaDineroDigitalRequest(
                        cajaGuiId, "Cuenta Digital 001", 500.0
                );

        when(cajaGuiService.initializeCajaDineroDigital(cajaGuiId, "Cuenta Digital 001", 500.0))
                .thenReturn(Mono.just(cajaGui));

        // When & Then
        StepVerifier.create(cajaGuiController.initializeCajaDineroDigital(request))
                .expectNext(cajaGui)
                .verifyComplete();

        verify(cajaGuiService).initializeCajaDineroDigital(cajaGuiId, "Cuenta Digital 001", 500.0);
    }

    @Test
    void testCloseCajaDineroEfectivo_Success() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        UUID cajaDineroEfectivoId = UUID.randomUUID();
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);

        CajaGuiController.CloseCajaDineroEfectivoRequest request = 
                new CajaGuiController.CloseCajaDineroEfectivoRequest(
                        cajaGuiId, cajaDineroEfectivoId,
                        0, 0, 0, 10, 5, 2, 1, 0, 0, 1, 0
                );

        when(cajaGuiService.closeCajaDineroEfectivo(
                eq(cajaGuiId), eq(cajaDineroEfectivoId),
                eq(0), eq(0), eq(0), eq(10), eq(5), eq(2), eq(1), eq(0), eq(0), eq(1), eq(0)))
                .thenReturn(Mono.just(cajaGui));

        // When & Then
        StepVerifier.create(cajaGuiController.closeCajaDineroEfectivo(request))
                .expectNext(cajaGui)
                .verifyComplete();

        verify(cajaGuiService).closeCajaDineroEfectivo(
                cajaGuiId, cajaDineroEfectivoId,
                0, 0, 0, 10, 5, 2, 1, 0, 0, 1, 0);
    }

    @Test
    void testCloseCajaDineroDigital_Success() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        UUID cajaDineroDigitalId = UUID.randomUUID();
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);

        CajaGuiController.CloseCajaDineroDigitalRequest request = 
                new CajaGuiController.CloseCajaDineroDigitalRequest(
                        cajaGuiId, cajaDineroDigitalId, 450.0
                );

        when(cajaGuiService.closeCajaDineroDigital(cajaGuiId, cajaDineroDigitalId, 450.0))
                .thenReturn(Mono.just(cajaGui));

        // When & Then
        StepVerifier.create(cajaGuiController.closeCajaDineroDigital(request))
                .expectNext(cajaGui)
                .verifyComplete();

        verify(cajaGuiService).closeCajaDineroDigital(cajaGuiId, cajaDineroDigitalId, 450.0);
    }

    private CajaGui createTestCajaGui(String nombre, Instant createdAt) {
        CajaGui cajaGui = new CajaGui();
        cajaGui.setId(UUID.randomUUID());
        cajaGui.setNombreCaja(nombre);
        cajaGui.setCreatedAt(createdAt);
        cajaGui.setGuiConfig("{}");
        return cajaGui;
    }
}
