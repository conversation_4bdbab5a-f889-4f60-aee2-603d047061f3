package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.model.CajaDineroDigital;
import corp.jamaro.jamaroservidor.app.caja.model.CajaDineroEfectivo;
import corp.jamaro.jamaroservidor.app.caja.model.gui.CajaGui;
import corp.jamaro.jamaroservidor.app.caja.repository.CajaDigitalRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.CajaEfectivoRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.gui.CajaGuiRepository;
import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.ventas.repository.SaleRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CajaGuiServiceTest {

    @Mock
    private TransactionalOperator transactionalOperator;
    
    @Mock
    private CajaGuiRepository cajaGuiRepository;
    
    @Mock
    private CajaEfectivoRepository cajaEfectivoRepository;
    
    @Mock
    private CajaDigitalRepository cajaDigitalRepository;
    
    @Mock
    private SaleRepository saleRepository;

    private CajaGuiService cajaGuiService;

    @BeforeEach
    void setUp() {
        cajaGuiService = new CajaGuiService(
                transactionalOperator,
                cajaGuiRepository,
                cajaEfectivoRepository,
                cajaDigitalRepository,
                saleRepository
        );
    }

    @Test
    void testGetAllCajaGuiOrderedByCreatedAt_Success() {
        // Given
        CajaGui cajaGui1 = createTestCajaGui("Caja 1", Instant.now().minusSeconds(3600));
        CajaGui cajaGui2 = createTestCajaGui("Caja 2", Instant.now());
        
        when(cajaGuiRepository.findAllOrderedByCreatedAtDesc())
                .thenReturn(Flux.just(cajaGui2, cajaGui1)); // Más reciente primero

        // When & Then
        StepVerifier.create(cajaGuiService.getAllCajaGuiOrderedByCreatedAt())
                .expectNext(cajaGui2)
                .expectNext(cajaGui1)
                .verifyComplete();

        verify(cajaGuiRepository).findAllOrderedByCreatedAtDesc();
    }

    @Test
    void testSubscribeToCurrentCajaGui_WithValidId_Success() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);

        when(cajaGuiRepository.findByIdWithRelations(cajaGuiId))
                .thenReturn(Mono.just(cajaGui));

        // When & Then
        StepVerifier.create(cajaGuiService.subscribeToCurrentCajaGui(cajaGuiId))
                .expectNext(cajaGui)
                .thenCancel()
                .verify();

        verify(cajaGuiRepository).findByIdWithRelations(cajaGuiId);
    }

    @Test
    void testSubscribeToCurrentCajaGui_WithNullId_ThrowsException() {
        // When & Then
        StepVerifier.create(cajaGuiService.subscribeToCurrentCajaGui(null))
                .expectError(IllegalArgumentException.class)
                .verify();

        verifyNoInteractions(cajaGuiRepository);
    }

    @Test
    void testSubscribeToCurrentCajaGui_WithNonExistentId_EmptyResult() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();

        when(cajaGuiRepository.findByIdWithRelations(cajaGuiId))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(cajaGuiService.subscribeToCurrentCajaGui(cajaGuiId))
                .verifyComplete();

        verify(cajaGuiRepository).findByIdWithRelations(cajaGuiId);
    }

    @Test
    void testCreateCajaGui_Success() {
        // Given
        String nombreCaja = "Nueva Caja";
        String guiConfig = "{\"theme\":\"dark\"}";
        CajaGui savedCajaGui = createTestCajaGui(nombreCaja, Instant.now());
        savedCajaGui.setGuiConfig(guiConfig);

        when(cajaGuiRepository.save(any(CajaGui.class)))
                .thenReturn(Mono.just(savedCajaGui));

        // When & Then
        StepVerifier.create(cajaGuiService.createCajaGui(nombreCaja, guiConfig))
                .expectNext(savedCajaGui)
                .verifyComplete();

        verify(cajaGuiRepository).save(any(CajaGui.class));
    }

    @Test
    void testCreateCajaGui_WithBlankName_ThrowsException() {
        // When & Then
        StepVerifier.create(cajaGuiService.createCajaGui("   ", "{}"))
                .expectError(IllegalArgumentException.class)
                .verify();

        verifyNoInteractions(cajaGuiRepository);
    }

    @Test
    void testCloseCajaDineroEfectivo_BreaksRelationship() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        UUID cajaDineroEfectivoId = UUID.randomUUID();
        
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);
        
        CajaDineroEfectivo cajaDineroEfectivo = new CajaDineroEfectivo();
        cajaDineroEfectivo.setId(cajaDineroEfectivoId);
        cajaDineroEfectivo.setEstaCerrada(false);
        cajaGui.setCajaDineroEfectivo(cajaDineroEfectivo);

        CajaGui cajaGuiAfterBreak = createTestCajaGui("Test Caja", Instant.now());
        cajaGuiAfterBreak.setId(cajaGuiId);
        cajaGuiAfterBreak.setCajaDineroEfectivo(null); // Relación rota

        User mockUser = new User();
        mockUser.setId(UUID.randomUUID());
        mockUser.setUsername("testuser");

        when(cajaGuiRepository.findById(cajaGuiId))
                .thenReturn(Mono.just(cajaGui))
                .thenReturn(Mono.just(cajaGuiAfterBreak));
        
        when(cajaEfectivoRepository.save(any(CajaDineroEfectivo.class)))
                .thenReturn(Mono.just(cajaDineroEfectivo));
        
        when(cajaGuiRepository.removeCajaDineroEfectivoRelation(cajaGuiId))
                .thenReturn(Mono.empty());
        
        when(transactionalOperator.transactional(any(Mono.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentUser)
                    .thenReturn(Mono.just(mockUser));

            // When & Then
            StepVerifier.create(cajaGuiService.closeCajaDineroEfectivo(
                    cajaGuiId, cajaDineroEfectivoId,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0))
                    .expectNext(cajaGuiAfterBreak)
                    .verifyComplete();

            verify(cajaGuiRepository).removeCajaDineroEfectivoRelation(cajaGuiId);
        }
    }

    @Test
    void testCloseCajaDineroDigital_BreaksRelationship() {
        // Given
        UUID cajaGuiId = UUID.randomUUID();
        UUID cajaDineroDigitalId = UUID.randomUUID();
        
        CajaGui cajaGui = createTestCajaGui("Test Caja", Instant.now());
        cajaGui.setId(cajaGuiId);
        
        CajaDineroDigital cajaDineroDigital = new CajaDineroDigital();
        cajaDineroDigital.setId(cajaDineroDigitalId);
        cajaDineroDigital.setEstaCerrada(false);
        cajaGui.setCajaDineroDigital(cajaDineroDigital);

        CajaGui cajaGuiAfterBreak = createTestCajaGui("Test Caja", Instant.now());
        cajaGuiAfterBreak.setId(cajaGuiId);
        cajaGuiAfterBreak.setCajaDineroDigital(null); // Relación rota

        User mockUser = new User();
        mockUser.setId(UUID.randomUUID());
        mockUser.setUsername("testuser");

        when(cajaGuiRepository.findById(cajaGuiId))
                .thenReturn(Mono.just(cajaGui))
                .thenReturn(Mono.just(cajaGuiAfterBreak));
        
        when(cajaDigitalRepository.save(any(CajaDineroDigital.class)))
                .thenReturn(Mono.just(cajaDineroDigital));
        
        when(cajaGuiRepository.removeCajaDineroDigitalRelation(cajaGuiId))
                .thenReturn(Mono.empty());
        
        when(transactionalOperator.transactional(any(Mono.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentUser)
                    .thenReturn(Mono.just(mockUser));

            // When & Then
            StepVerifier.create(cajaGuiService.closeCajaDineroDigital(
                    cajaGuiId, cajaDineroDigitalId, 100.0))
                    .expectNext(cajaGuiAfterBreak)
                    .verifyComplete();

            verify(cajaGuiRepository).removeCajaDineroDigitalRelation(cajaGuiId);
        }
    }

    private CajaGui createTestCajaGui(String nombre, Instant createdAt) {
        CajaGui cajaGui = new CajaGui();
        cajaGui.setId(UUID.randomUUID());
        cajaGui.setNombreCaja(nombre);
        cajaGui.setCreatedAt(createdAt);
        cajaGui.setGuiConfig("{}");
        return cajaGui;
    }
}
