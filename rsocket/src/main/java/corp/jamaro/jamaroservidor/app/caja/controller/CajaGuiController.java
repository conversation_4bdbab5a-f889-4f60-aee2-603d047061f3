package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.model.gui.CajaGui;
import corp.jamaro.jamaroservidor.app.caja.service.CajaGuiService;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Controller
@RequiredArgsConstructor
@Slf4j
@MessageMapping("cajaGui")
public class CajaGuiController {

    private final CajaGuiService cajaGuiService;

    /**
     * Permite a los clientes suscribirse a actualizaciones de la lista de Sales pendientes de cobro.
     *
     * Los clientes recibirán:
     * 1. La lista actual de Sales con estaPagadoEntregado = false
     * 2. Actualizaciones en tiempo real cuando cambien los Sales pendientes
     *
     * Los Sales se ordenan por createdAt de más reciente a más antiguo.
     *
     * @return Flux que emite listas de Sales pendientes de cobro
     */
    @MessageMapping("subscribeToSalesPorCobrar")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
    public Flux<List<Sale>> subscribeToSalesPorCobrar() {
        log.info("Cliente suscribiéndose a Sales por cobrar");
        return cajaGuiService.subscribeToSalesPorCobrar();
    }

    /**
     * Busca Sales completadas (estaPagadoEntregado = true) con filtros opcionales.
     *
     * Permite filtrar por:
     * - ID específico del Sale
     * - ID del User que inició la venta
     * - Datos de documento del Cliente (DNI, RUC, otroDocumento)
     * - Nombre, apellido o razón social del Cliente (búsqueda parcial)
     * - Código compuesto del Item (búsqueda parcial)
     * - Tipo de venta
     * - Fecha límite
     *
     * Si no se proporcionan filtros, retorna las últimas 99 ventas completadas.
     * Si hay filtros, retorna máximo 33 resultados.
     *
     * @param request Parámetros de búsqueda (todos opcionales)
     * @return Flux<Sale> Lista de Sales que coinciden con los filtros
     */
    @MessageMapping("buscarSalesCompletadas")
    public Flux<Sale> buscarSalesCompletadas(SaleBusquedaRequest request) {
        log.info("Buscando Sales completadas con filtros: {}", request);

        // Extraer parámetros del request (pueden ser null)
        return cajaGuiService.buscarSalesCompletadas(
                request.saleId(),
                request.iniciadaPorId(),
                request.datoDocumento(),
                request.nombreCliente(),
                request.codCompuesto(),
                request.tipoVenta(),
                request.fechaLimite()
        );
    }

    /**
     * Busca Sales incompletas (estaPagadoEntregado = false) filtradas por User que las inició.
     *
     * Si iniciadaPorId es null o vacío, retorna las últimas 99 ventas incompletas.
     * Si iniciadaPorId no es null, retorna todas las ventas incompletas de ese usuario.
     * Excluye las ventas de tipo PROFORMA.
     * Ordena por fecha de creación de más reciente a más antiguo.
     *
     * @param request Parámetros de búsqueda con iniciadaPorId
     * @return Flux<Sale> Lista de Sales incompletas ordenadas por createdAt DESC
     */
    @MessageMapping("buscarSalesIncompletas")
    public Flux<Sale> buscarSalesIncompletas(SaleIncompletasBusquedaRequest request) {
        log.info("Buscando Sales incompletas para usuario: {}", request.iniciadaPorId());

        return cajaGuiService.buscarSalesIncompletas(request.iniciadaPorId());
    }

    /**
     * Permite a los clientes suscribirse a actualizaciones de la CajaGui actual.
     *
     * Los clientes recibirán:
     * 1. La CajaGui actual del usuario autenticado (si existe)
     * 2. Actualizaciones en tiempo real cuando se modifique la CajaGui
     *
     * @return Flux que emite la CajaGui actual y sus actualizaciones futuras
     */
    @MessageMapping("subscribeToCurrentCajaGui")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
    public Flux<CajaGui> subscribeToCurrentCajaGui() {
        log.info("Cliente suscribiéndose a actualizaciones de CajaGui");
        return cajaGuiService.subscribeToCurrentCajaGui();
    }

    /**
     * Crea una nueva CajaGui.
     *
     * @param request Datos para crear la CajaGui
     * @return Mono<CajaGui> La CajaGui creada
     */
    @MessageMapping("createCajaGui")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
    public Mono<CajaGui> createCajaGui(CreateCajaGuiRequest request) {
        log.info("Creando nueva CajaGui: {}", request.nombreCaja());
        return cajaGuiService.createCajaGui(request.nombreCaja(), request.guiConfig());
    }

    /**
     * Inicializa una CajaDineroEfectivo para una CajaGui existente.
     *
     * @param request Datos para inicializar la CajaDineroEfectivo
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroEfectivo
     */
    @MessageMapping("initializeCajaDineroEfectivo")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
    public Mono<CajaGui> initializeCajaDineroEfectivo(InitializeCajaDineroEfectivoRequest request) {
        log.info("Inicializando CajaDineroEfectivo para CajaGui ID: {}", request.cajaGuiId());

        return cajaGuiService.initializeCajaDineroEfectivo(
                request.cajaGuiId(),
                request.nombre(),
                request.montoInicialEfectivo(),
                request.diezCentimos(),
                request.veinteCentimos(),
                request.cincuentaCentimos(),
                request.unSol(),
                request.dosSoles(),
                request.cincoSoles(),
                request.diezSoles(),
                request.veinteSoles(),
                request.cincuentaSoles(),
                request.cienSoles(),
                request.doscientosSoles()
        );
    }

    /**
     * Inicializa una CajaDineroDigital para una CajaGui existente.
     *
     * @param request Datos para inicializar la CajaDineroDigital
     * @return Mono<CajaGui> La CajaGui actualizada con la CajaDineroDigital
     */
    @MessageMapping("initializeCajaDineroDigital")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
    public Mono<CajaGui> initializeCajaDineroDigital(InitializeCajaDineroDigitalRequest request) {
        log.info("Inicializando CajaDineroDigital para CajaGui ID: {}", request.cajaGuiId());

        return cajaGuiService.initializeCajaDineroDigital(
                request.cajaGuiId(),
                request.cuentaDigitalAsignada(),
                request.montoInicialDigital()
        );
    }

    /**
     * Cierra una CajaDineroEfectivo con los detalles de cierre.
     *
     * @param request Datos para cerrar la CajaDineroEfectivo
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    @MessageMapping("closeCajaDineroEfectivo")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
    public Mono<CajaGui> closeCajaDineroEfectivo(CloseCajaDineroEfectivoRequest request) {
        log.info("Cerrando CajaDineroEfectivo ID: {} para CajaGui ID: {}",
                request.cajaDineroEfectivoId(), request.cajaGuiId());

        return cajaGuiService.closeCajaDineroEfectivo(
                request.cajaGuiId(),
                request.cajaDineroEfectivoId(),
                request.cierreDiezCentimos(),
                request.cierreVeinteCentimos(),
                request.cierreCincuentaCentimos(),
                request.cierreUnSol(),
                request.cierreDosSoles(),
                request.cierreCincoSoles(),
                request.cierreDiezSoles(),
                request.cierreVeinteSoles(),
                request.cierreCincuentaSoles(),
                request.cierreCienSoles(),
                request.cierreDoscientosSoles()
        );
    }

    /**
     * Cierra una CajaDineroDigital con el monto declarado.
     *
     * @param request Datos para cerrar la CajaDineroDigital
     * @return Mono<CajaGui> La CajaGui actualizada con la caja cerrada
     */
    @MessageMapping("closeCajaDineroDigital")
    @PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
    public Mono<CajaGui> closeCajaDineroDigital(CloseCajaDineroDigitalRequest request) {
        log.info("Cerrando CajaDineroDigital ID: {} para CajaGui ID: {}",
                request.cajaDineroDigitalId(), request.cajaGuiId());

        return cajaGuiService.closeCajaDineroDigital(
                request.cajaGuiId(),
                request.cajaDineroDigitalId(),
                request.cierreDigitalDeclarado()
        );
    }

    // Records para agrupar los parámetros de cada solicitud

    /**
     * Datos para la solicitud de búsqueda de Sales completadas.
     */
    public record SaleBusquedaRequest(
            UUID saleId,
            UUID iniciadaPorId,
            String datoDocumento,
            String nombreCliente,
            String codCompuesto,
            String tipoVenta,
            Instant fechaLimite
    ) {}

    /**
     * Datos para la solicitud de crear una nueva CajaGui.
     */
    public record CreateCajaGuiRequest(
            String nombreCaja,
            String guiConfig
    ) {}

    /**
     * Datos para la solicitud de inicializar una CajaDineroEfectivo.
     */
    public record InitializeCajaDineroEfectivoRequest(
            UUID cajaGuiId,
            String nombre,
            Double montoInicialEfectivo,
            Integer diezCentimos,
            Integer veinteCentimos,
            Integer cincuentaCentimos,
            Integer unSol,
            Integer dosSoles,
            Integer cincoSoles,
            Integer diezSoles,
            Integer veinteSoles,
            Integer cincuentaSoles,
            Integer cienSoles,
            Integer doscientosSoles
    ) {}

    /**
     * Datos para la solicitud de inicializar una CajaDineroDigital.
     */
    public record InitializeCajaDineroDigitalRequest(
            UUID cajaGuiId,
            String cuentaDigitalAsignada,
            Double montoInicialDigital
    ) {}

    /**
     * Datos para la solicitud de cerrar una CajaDineroEfectivo.
     */
    public record CloseCajaDineroEfectivoRequest(
            UUID cajaGuiId,
            UUID cajaDineroEfectivoId,
            Integer cierreDiezCentimos,
            Integer cierreVeinteCentimos,
            Integer cierreCincuentaCentimos,
            Integer cierreUnSol,
            Integer cierreDosSoles,
            Integer cierreCincoSoles,
            Integer cierreDiezSoles,
            Integer cierreVeinteSoles,
            Integer cierreCincuentaSoles,
            Integer cierreCienSoles,
            Integer cierreDoscientosSoles
    ) {}

    /**
     * Datos para la solicitud de cerrar una CajaDineroDigital.
     */
    public record CloseCajaDineroDigitalRequest(
            UUID cajaGuiId,
            UUID cajaDineroDigitalId,
            Double cierreDigitalDeclarado
    ) {}

    /**
     * Datos para la solicitud de búsqueda de Sales incompletas.
     */
    public record SaleIncompletasBusquedaRequest(
            UUID iniciadaPorId
    ) {}
}
